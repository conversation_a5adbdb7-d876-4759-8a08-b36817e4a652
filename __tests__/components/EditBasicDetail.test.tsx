import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import EditBasicDetailsComp from '../../src/components/EditBasicDetail';
import {TemplateForm} from '../../src/types/template';
import {RiskForm} from '../../src/types/risk';
import {TemplateFormStatus} from '../../src/enums';
import {useDataStoreContext} from '../../src/context';

// Mock dependencies
jest.mock('../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../src/components/DropdownTypeahead', () => {
  return function MockDropdownTypeahead({
    label,
    options,
    selected,
    onChange,
    multiple,
    isInvalid,
    errorMessage,
    useCheckboxes,
  }: any) {
    return (
      <div data-testid={`dropdown-${label.toLowerCase().replace(/\s+/g, '-')}`}>
        <label>{label}</label>
        <select
          data-testid={`select-${label.toLowerCase().replace(/\s+/g, '-')}`}
          multiple={multiple}
          onChange={(e) => {
            const value = multiple
              ? Array.from(e.target.selectedOptions, (option: any) => ({
                  value: parseInt(option.value),
                  label: option.text,
                }))
              : options.find((opt: any) => opt.value === parseInt(e.target.value));
            onChange(value);
          }}
          className={isInvalid ? 'is-invalid' : ''}
        >
          {options.map((option: any) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {isInvalid && errorMessage && (
          <div className="invalid-feedback">{errorMessage}</div>
        )}
      </div>
    );
  };
});

jest.mock('../../src/components/SingleVesselOfficeDropdown', () => {
  return function MockSingleVesselOfficeDropdown({
    label,
    options,
    selected,
    onChange,
    isInvalid,
    errorMessage,
  }: any) {
    return (
      <div data-testid="single-vessel-office-dropdown">
        <label>{label}</label>
        <select
          data-testid="vessel-office-select"
          onChange={(e) => {
            const selectedOption = options
              .flatMap((group: any) => group.options)
              .find((opt: any) => opt.value === e.target.value);
            onChange(selectedOption);
          }}
          className={isInvalid ? 'is-invalid' : ''}
        >
          <option value="">Select...</option>
          {options.map((group: any) => (
            <optgroup key={group.label} label={group.label}>
              {group.options.map((option: any) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </optgroup>
          ))}
        </select>
        {isInvalid && errorMessage && (
          <div className="invalid-feedback">{errorMessage}</div>
        )}
      </div>
    );
  };
});

jest.mock('../../src/components/CustomDatePicker', () => {
  return function MockCustomDatePicker({
    label,
    selectedDate,
    onChange,
    isInvalid,
    errorMessage,
  }: any) {
    return (
      <div data-testid="custom-date-picker">
        <label>{label}</label>
        <input
          type="date"
          data-testid="date-input"
          value={selectedDate || ''}
          onChange={(e) => onChange(e.target.value ? new Date(e.target.value) : undefined)}
          className={isInvalid ? 'is-invalid' : ''}
        />
        {isInvalid && errorMessage && (
          <div className="invalid-feedback">{errorMessage}</div>
        )}
      </div>
    );
  };
});

jest.mock('../../src/utils/helper', () => ({
  formatDateToYYYYMMDD: jest.fn((date: Date) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  }),
  createGroupedVesselOfficeOptions: jest.fn(() => [
    {
      label: 'Vessels',
      options: [
        {value: '1', label: 'Vessel 1', vesselId: 101},
        {value: '2', label: 'Vessel 2', vesselId: 102},
      ],
    },
    {
      label: 'Offices',
      options: [
        {value: '3', label: 'Office 1'},
        {value: '4', label: 'Office 2'},
      ],
    },
  ]),
  findSelectedVesselOfficeOption: jest.fn(),
}));

jest.mock('../../src/utils/common', () => ({
  vesselStatusAndLabelName: {
    1: 'Active',
    2: 'Inactive',
  },
}));

describe('EditBasicDetailsComp', () => {
  const mockUseDataStoreContext = useDataStoreContext as jest.MockedFunction<
    typeof useDataStoreContext
  >;

  const mockDataStore = {
    riskCategoryList: [],
    hazardsList: [],
    riskParameterType: [],
    riskParameterList: [],
    taskReliabilityAssessList: [],
    riskParameterListForRiskRaiting: [],
    vesselListForRisk: [
      {id: 1, name: 'Vessel 1', code: 'V001'},
      {id: 2, name: 'Vessel 2', code: 'V002'},
    ],
    officeListForRisk: [
      {id: 1, name: 'Office 1'},
      {id: 2, name: 'Office 2'},
    ],
    approversReqListForRiskOffice: [
      {id: 1, name: 'Office Approver 1'},
      {id: 2, name: 'Office Approver 2'},
    ],
    approversReqListForRiskVessel: [
      {id: 3, name: 'Vessel Approver 1'},
      {id: 4, name: 'Vessel Approver 2'},
    ],
    crewMembersListForRisk: [],
  };

  const mockTemplateForm: TemplateForm = {
    task_requiring_ra: 'Test Task',
    task_duration: '2 days',
    task_alternative_consideration: 'Test alternative',
    task_rejection_reason: 'Test reason',
    worst_case_scenario: 'Test scenario',
    recovery_measures: 'Test measures',
    status: TemplateFormStatus.DRAFT,
    template_category: {
      category_id: [],
      is_other: false,
      value: '',
    },
    template_hazard: {
      is_other: false,
      value: '',
      hazard_id: [],
    },
    parameters: [],
    template_job: [],
    template_task_reliability_assessment: [],
    template_keyword: [],
  };

  const mockRiskForm: RiskForm = {
    task_requiring_ra: 'Risk Task',
    assessor: 1,
    vessel_ownership_id: 1,
    vessel_id: 101,
    date_risk_assessment: '2024-01-15',
    task_duration: '3 days',
    task_alternative_consideration: 'Risk alternative',
    task_rejection_reason: 'Risk reason',
    worst_case_scenario: 'Risk scenario',
    recovery_measures: 'Risk measures',
    status: 'draft',
    approval_required: [1, 2],
    risk_team_member: [],
    risk_category: {
      is_other: false,
      category_id: [],
      value: '',
    },
    risk_hazard: {
      is_other: false,
      hazard_id: [],
      value: '',
    },
    parameters: [],
    risk_job: [],
    risk_task_reliability_assessment: [],

  };

  const mockSetClonedForm = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDataStoreContext.mockReturnValue({
      dataStore: mockDataStore as any,
      setDataStore: jest.fn(),
      roleConfig: {
        user: {user_id: 'test-user'} as any,
        riskAssessment: {hasPermision: true, canCreateNewTemplate: true},
      },
      ga4EventTrigger: jest.fn(),
    });
  });

  describe('Template Form Rendering', () => {
    it('renders basic template form fields', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      expect(screen.getByLabelText('Task Requiring R.A.')).toBeInTheDocument();
      expect(screen.getByLabelText('Task Duration')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Task')).toBeInTheDocument();
      expect(screen.getByDisplayValue('2 days')).toBeInTheDocument();
    });

    it('handles task requiring RA input changes', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      const taskInput = screen.getByDisplayValue('Test Task');
      fireEvent.change(taskInput, {target: {value: 'Updated Task'}});

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...mockTemplateForm,
        task_requiring_ra: 'Updated Task',
      });
    });

    it('handles task duration input changes', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      const durationInput = screen.getByDisplayValue('2 days');
      fireEvent.change(durationInput, {target: {value: '5 days'}});

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...mockTemplateForm,
        task_duration: '5 days',
      });
    });

    it('shows validation errors for empty required fields', () => {
      const emptyForm = {...mockTemplateForm, task_requiring_ra: ''};
      render(
        <EditBasicDetailsComp
          clonedForm={emptyForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      expect(taskInput).toHaveClass('is-invalid');
      // Use getAllByText since there might be multiple error messages
      const errorMessages = screen.getAllByText('This is a mandatory field. Please fill to process.');
      expect(errorMessages.length).toBeGreaterThan(0);
    });

    it('enforces maxLength on task requiring RA field', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      const taskInput = screen.getByDisplayValue('Test Task');
      expect(taskInput).toHaveAttribute('maxLength', '255');
    });

    it('enforces maxLength on task duration field', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      const durationInput = screen.getByDisplayValue('2 days');
      expect(durationInput).toHaveAttribute('maxLength', '255');
    });

    it('shows placeholder text for task duration', () => {
      const emptyForm = {...mockTemplateForm, task_duration: ''};
      render(
        <EditBasicDetailsComp
          clonedForm={emptyForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      const durationInput = screen.getByPlaceholderText('Enter No. of Days Required');
      expect(durationInput).toBeInTheDocument();
    });

    it('shows helper text for task duration', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      expect(screen.getByText('Mention if values are in Days/Hours')).toBeInTheDocument();
    });
  });

  describe('Risk Form Rendering', () => {
    it('renders risk form with additional fields', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        expect(screen.getByLabelText('Task Requiring R.A.')).toBeInTheDocument();
        expect(screen.getByLabelText('Task Duration')).toBeInTheDocument();
        expect(screen.getByTestId('dropdown-assessor')).toBeInTheDocument();
        expect(screen.getByTestId('single-vessel-office-dropdown')).toBeInTheDocument();
        expect(screen.getByTestId('custom-date-picker')).toBeInTheDocument();
        expect(screen.getByTestId('dropdown-approvals-required-(if-necessary)')).toBeInTheDocument();
      });
    });

    it('handles assessor dropdown changes', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        const assessorSelect = screen.getByTestId('select-assessor');
        fireEvent.change(assessorSelect, {target: {value: '2'}});
      });

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        assessor: 2,
      });
    });

    it('handles vessel/office dropdown changes', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        const vesselOfficeSelect = screen.getByTestId('vessel-office-select');
        fireEvent.change(vesselOfficeSelect, {target: {value: '1'}});
      });

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        vessel_ownership_id: 1,
        vessel_id: 101,
      });
    });

    it('handles date changes', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        const dateInput = screen.getByTestId('date-input');
        fireEvent.change(dateInput, {target: {value: '2024-02-15'}});
      });

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        date_risk_assessment: '2024-02-15',
      });
    });

    it('handles approval required changes for office assessor', async () => {
      const officeAssessorForm = {...mockRiskForm, assessor: 1};
      render(
        <EditBasicDetailsComp
          clonedForm={officeAssessorForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        const approvalSelect = screen.getByTestId('select-approvals-required-(if-necessary)');
        fireEvent.change(approvalSelect, {target: {value: ['1']}});
      });

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...officeAssessorForm,
        approval_required: [1],
      });
    });

    it('handles approval required changes for vessel assessor', async () => {
      const vesselAssessorForm = {...mockRiskForm, assessor: 2};
      render(
        <EditBasicDetailsComp
          clonedForm={vesselAssessorForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        const approvalSelect = screen.getByTestId('select-approvals-required-(if-necessary)');
        fireEvent.change(approvalSelect, {target: {value: ['3']}});
      });

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...vesselAssessorForm,
        approval_required: [3],
      });
    });

    it('shows validation errors for empty risk form fields', async () => {
      const emptyRiskForm = {
        ...mockRiskForm,
        task_requiring_ra: '',
        task_duration: '',
        assessor: 0,
        vessel_ownership_id: 0,
        date_risk_assessment: '',
        approval_required: [],
      };

      render(
        <EditBasicDetailsComp
          clonedForm={emptyRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        expect(screen.getByLabelText('Task Requiring R.A.')).toHaveClass('is-invalid');
        expect(screen.getByLabelText('Task Duration')).toHaveClass('is-invalid');
        expect(screen.getByTestId('select-assessor')).toHaveClass('is-invalid');
        expect(screen.getByTestId('vessel-office-select')).toHaveClass('is-invalid');
        // Date input validation might be handled differently by CustomDatePicker
        expect(screen.getByTestId('date-input')).toBeInTheDocument();
        expect(screen.getByTestId('select-approvals-required-(if-necessary)')).toHaveClass('is-invalid');
      });
    });

    it('loads vessel and office options on mount for risk type', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('single-vessel-office-dropdown')).toBeInTheDocument();
        expect(screen.getByTestId('dropdown-approvals-required-(if-necessary)')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles errors in loading vessel/office options gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      // Mock helper function to throw error
      const mockCreateGroupedVesselOfficeOptions = require('../../src/utils/helper').createGroupedVesselOfficeOptions;
      mockCreateGroupedVesselOfficeOptions.mockImplementationOnce(() => {
        throw new Error('Failed to load options');
      });

      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error loading vessel/office options:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    it('handles missing data store values gracefully', () => {
      mockUseDataStoreContext.mockReturnValue({
        dataStore: {
          ...mockDataStore,
          vesselListForRisk: [],
          officeListForRisk: [],
          approversReqListForRiskOffice: [],
          approversReqListForRiskVessel: [],
        } as any,
        setDataStore: jest.fn(),
        roleConfig: {
          user: {user_id: 'test-user'} as any,
          riskAssessment: {hasPermision: true, canCreateNewTemplate: true},
        },
        ga4EventTrigger: jest.fn(),
      });

      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      expect(screen.getByLabelText('Task Requiring R.A.')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles undefined clonedForm values', () => {
      const undefinedForm = {
        ...mockTemplateForm,
        task_requiring_ra: undefined as any,
        task_duration: undefined as any,
      };

      render(
        <EditBasicDetailsComp
          clonedForm={undefinedForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      expect(screen.getByLabelText('Task Requiring R.A.')).toHaveValue('');
      expect(screen.getByLabelText('Task Duration')).toHaveValue('');
    });

    it('handles null vessel_ownership_id in risk form', async () => {
      const nullVesselForm = {...mockRiskForm, vessel_ownership_id: null as any};

      render(
        <EditBasicDetailsComp
          clonedForm={nullVesselForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('vessel-office-select')).toHaveClass('is-invalid');
      });
    });

    it('handles empty approval_required array', async () => {
      const emptyApprovalForm = {...mockRiskForm, approval_required: []};

      render(
        <EditBasicDetailsComp
          clonedForm={emptyApprovalForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('select-approvals-required-(if-necessary)')).toHaveClass('is-invalid');
      });
    });

    it('handles switching between assessor types', async () => {
      const {rerender} = render(
        <EditBasicDetailsComp
          clonedForm={{...mockRiskForm, assessor: 1}}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('dropdown-approvals-required-(if-necessary)')).toBeInTheDocument();
      });

      rerender(
        <EditBasicDetailsComp
          clonedForm={{...mockRiskForm, assessor: 2}}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('dropdown-approvals-required-(if-necessary)')).toBeInTheDocument();
      });
    });

    it('handles template type with default props', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
        />
      );

      expect(screen.getByLabelText('Task Requiring R.A.')).toBeInTheDocument();
      expect(screen.getByLabelText('Task Duration')).toBeInTheDocument();
      // Should not render risk-specific fields
      expect(screen.queryByTestId('dropdown-assessor')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper form labels and controls', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      const durationInput = screen.getByLabelText('Task Duration');

      expect(taskInput).toHaveAttribute('type', 'text');
      expect(durationInput).toHaveAttribute('type', 'text');
    });

    it('shows proper error messages with invalid feedback', () => {
      const emptyForm = {
        ...mockTemplateForm,
        task_requiring_ra: '',
        task_duration: '',
      };

      render(
        <EditBasicDetailsComp
          clonedForm={emptyForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />
      );

      const errorMessages = screen.getAllByText('This is a mandatory field. Please fill to process.');
      expect(errorMessages).toHaveLength(2);
    });
  });
});