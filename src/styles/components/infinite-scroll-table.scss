.infinite-scroll-table-container {
  .table {
    background: white;
    overflow-x: auto;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 14px;

    .header {
      background-color: #fff;
      position: sticky;
      top: 0;
      z-index: 1;

      .th {
        padding: 12px 8px;
        font-weight: 600;
        cursor: pointer;
        line-height: 20px;
        transition: background 0.3s ease;
        border-bottom: 3px solid #1f4a70 !important;

        &:hover {
          background: #f5f5f5;
        }
      }
    }

    tbody {
      tr {
        border-bottom: 1px solid #f0f0f0;
        transition: background 0.3s ease;

        &:hover {
          background: #fafafa;
        }

        td {
          padding: 6px 8px;
        }
      }
    }
  }

  .clickable-row {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .sticky-styles-right {
    position: sticky !important;
    right: 0 !important;
    border-left: 1px solid #dee2e6 !important;
    background: #fff !important;
  }

  .sticky-styles-left {
    position: sticky !important;
    left: 0 !important;
    border-right: 1px solid #dee2e6 !important;
    background: #fff !important;
  }

  // Ensure dropdown menus can overflow the table container
  .table-responsive {
    overflow: visible !important;
  }

  // Allow dropdown menus to appear above table content
  tbody tr:has(.ra-three-dots-dropdown.show) {
    position: relative;
    z-index: 1051;
  }
}
