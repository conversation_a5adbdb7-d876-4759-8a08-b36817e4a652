<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/ActionsDropdownCell.test.tsx">
        <testCase name="ActionsDropdownCell Component Rendering should render the dropdown component" duration="0"/>
        <testCase name="ActionsDropdownCell Component Rendering should render the three dots icon" duration="0"/>
        <testCase name="ActionsDropdownCell Component Rendering should render dropdown toggle with correct classes" duration="0"/>
        <testCase name="ActionsDropdownCell Component Rendering should render dropdown structure" duration="0"/>
        <testCase name="ActionsDropdownCell Dropdown Menu Items should render Edit and Delete menu items when dropdown is opened" duration="0"/>
        <testCase name="ActionsDropdownCell Dropdown Menu Items should render component without errors" duration="0"/>
        <testCase name="ActionsDropdownCell Edit Action Handler should call all required functions when Edit is clicked" duration="0"/>
        <testCase name="ActionsDropdownCell Edit Action Handler should call functions with correct parameters for different jobId" duration="0"/>
        <testCase name="ActionsDropdownCell Delete Action Handler should call required functions when Delete is clicked" duration="0"/>
        <testCase name="ActionsDropdownCell Delete Action Handler should call functions with correct jobId for different job" duration="0"/>
        <testCase name="ActionsDropdownCell Props Validation should handle empty jobId" duration="0"/>
        <testCase name="ActionsDropdownCell Props Validation should render without crashing when all props are provided" duration="0"/>
        <testCase name="ActionsDropdownCell Component Structure should have correct dropdown structure" duration="0"/>
        <testCase name="ActionsDropdownCell Component Structure should have proper CSS classes applied" duration="0"/>
        <testCase name="ActionsDropdownCell Component Structure should have dropdown menu with correct classes when opened" duration="0"/>
        <testCase name="ActionsDropdownCell Accessibility should have proper dropdown structure for screen readers" duration="0"/>
        <testCase name="ActionsDropdownCell Accessibility should render menu items with proper text content when opened" duration="0"/>
        <testCase name="ActionsDropdownCell Dropdown Interaction should toggle dropdown when clicked" duration="52"/>
        <testCase name="ActionsDropdownCell Dropdown Interaction should have proper popper configuration" duration="0"/>
        <testCase name="ActionsDropdownCell Edge Cases and Error Handling should handle null or undefined jobId gracefully" duration="0"/>
        <testCase name="ActionsDropdownCell Edge Cases and Error Handling should handle special characters in jobId" duration="0"/>
        <testCase name="ActionsDropdownCell Edge Cases and Error Handling should maintain component state after multiple interactions" duration="0"/>
        <testCase name="ActionsDropdownCell Performance and Optimization should not re-render unnecessarily when props do not change" duration="0"/>
        <testCase name="ActionsDropdownCell Performance and Optimization should handle rapid successive clicks gracefully" duration="0"/>
    </file>
</testExecutions>