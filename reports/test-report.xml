<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/DeleteJobModal.test.tsx">
        <testCase name="DeleteJobModal renders modal with correct title and content" duration="50"/>
        <testCase name="DeleteJobModal renders Delete Job Step and Cancel buttons" duration="17"/>
        <testCase name="DeleteJobModal displays correct job information for the selected job" duration="8"/>
        <testCase name="DeleteJobModal displays correct job information for first job" duration="5"/>
        <testCase name="DeleteJobModal displays correct job information for last job" duration="7"/>
        <testCase name="DeleteJobModal calls onClose when Cancel button is clicked" duration="8"/>
        <testCase name="DeleteJobModal removes job from form and calls onClose when Delete Job Step is clicked" duration="9"/>
        <testCase name="DeleteJobModal handles empty template_job array" duration="4"/>
        <testCase name="DeleteJobModal handles non-existent jobId" duration="4"/>
        <testCase name="DeleteJobModal handles undefined template_job" duration="5"/>
        <testCase name="DeleteJobModal renders with correct CSS classes and structure" duration="6"/>
        <testCase name="DeleteJobModal renders modal with correct properties" duration="5"/>
        <testCase name="DeleteJobModal handles job deletion when jobId is empty string" duration="7"/>
        <testCase name="DeleteJobModal preserves other form properties when deleting job" duration="6"/>
        <testCase name="DeleteJobModal Risk Form Tests renders modal correctly for risk form" duration="4"/>
        <testCase name="DeleteJobModal Risk Form Tests displays correct job information for risk form" duration="5"/>
        <testCase name="DeleteJobModal Risk Form Tests displays correct job information for first risk job" duration="4"/>
        <testCase name="DeleteJobModal Risk Form Tests displays correct job information for last risk job" duration="4"/>
        <testCase name="DeleteJobModal Risk Form Tests removes job from risk form and calls onClose when Delete Job Step is clicked" duration="8"/>
        <testCase name="DeleteJobModal Risk Form Tests handles invalid jobId for risk form" duration="3"/>
        <testCase name="DeleteJobModal Risk Form Tests handles out of bounds jobId for risk form" duration="3"/>
        <testCase name="DeleteJobModal Risk Form Tests handles empty risk_job array" duration="3"/>
        <testCase name="DeleteJobModal Risk Form Tests handles undefined risk_job" duration="5"/>
        <testCase name="DeleteJobModal Risk Form Tests preserves other risk form properties when deleting job" duration="6"/>
        <testCase name="DeleteJobModal Risk Form Tests handles job deletion when jobId is negative for risk form" duration="6"/>
    </file>
</testExecutions>