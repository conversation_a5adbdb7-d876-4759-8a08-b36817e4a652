<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/PreviewFormDetails.test.tsx">
        <testCase name="PreviewFormDetails Component Component Rendering renders the component with all main sections" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays task title correctly" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays task duration correctly" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays default task title when not provided" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays dash when task duration is not provided" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Rendering renders infinite scroll table with correct data" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Rendering renders table with empty data when no jobs" duration="0"/>
        <testCase name="PreviewFormDetails Component Breadcrumb Navigation renders breadcrumb with correct items" duration="0"/>
        <testCase name="PreviewFormDetails Component Breadcrumb Navigation renders empty breadcrumb item when task title is empty" duration="0"/>
        <testCase name="PreviewFormDetails Component Form Input Interactions handles alternative consideration textarea change" duration="0"/>
        <testCase name="PreviewFormDetails Component Form Input Interactions handles rejection reason textarea change" duration="0"/>
        <testCase name="PreviewFormDetails Component Form Input Interactions displays current values in textareas" duration="0"/>
        <testCase name="PreviewFormDetails Component Button Interactions calls handlePreviewPublish when Publish Template button is clicked" duration="0"/>
        <testCase name="PreviewFormDetails Component Button Interactions calls handleSaveToDraft when Save to Draft button is clicked" duration="0"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens guidance PDF when Guidance Table button is clicked" duration="0"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens risk matrix PDF when Risk Matrix Table button is clicked" duration="0"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens guidance table PDF when button is clicked" duration="0"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens risk matrix PDF when button is clicked" duration="0"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display displays risk categories as badges" duration="0"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display handles categories with is_other flag" duration="0"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display handles hazards with is_other flag" duration="0"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display displays empty categories when no categories selected" duration="0"/>
        <testCase name="PreviewFormDetails Component At Risk Parameters Display displays parameter names in uppercase" duration="0"/>
        <testCase name="PreviewFormDetails Component At Risk Parameters Display handles parameters with custom values" duration="0"/>
        <testCase name="PreviewFormDetails Component At Risk Parameters Display handles empty parameters array" duration="0"/>
        <testCase name="PreviewFormDetails Component Table Configuration renders table with correct column count" duration="0"/>
        <testCase name="PreviewFormDetails Component Table Configuration displays action menu icon in table" duration="0"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling handles undefined form properties gracefully" duration="0"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling handles empty data store gracefully" duration="0"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling handles missing dataStore properties" duration="0"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling displays user name correctly when user is creator/updater" duration="0"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling displays --- when user is not creator/updater" duration="0"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling renders edit buttons with correct icons" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Integration passes correct props to RiskRatingStep" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Integration passes correct props to BottomButton" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Integration renders input components with correct labels" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Integration hides action buttons when previewOnly is true" duration="0"/>
        <testCase name="PreviewFormDetails Component Component Integration shows correct button text for risk type" duration="0"/>
        <testCase name="PreviewFormDetails Component Styling and Layout applies correct styling to main sections" duration="0"/>
        <testCase name="PreviewFormDetails Component Styling and Layout renders badges with correct styling attributes" duration="0"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles different form configurations" duration="0"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles empty arrays in form data" duration="0"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles button interactions" duration="0"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality renders with preview mode correctly" duration="0"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles edit hazard categories button click" duration="0"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles edit at-risk parameters button click" duration="0"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles add job modal close" duration="0"/>
        <testCase name="PreviewFormDetails Component Edit Functionality renders edit basic details button and handles click" duration="0"/>
        <testCase name="PreviewFormDetails Component Edit Functionality renders edit category buttons" duration="0"/>
        <testCase name="PreviewFormDetails Component Edit Functionality renders table and maintains state after interactions" duration="0"/>
        <testCase name="PreviewFormDetails Component Modal Interactions renders add job button and handles click" duration="0"/>
        <testCase name="PreviewFormDetails Component Modal Interactions renders table with action menu" duration="0"/>
        <testCase name="PreviewFormDetails Component Modal Interactions renders guidance and risk matrix buttons" duration="0"/>
        <testCase name="PreviewFormDetails Component Modal Close Functions Coverage covers onEditClose function with editStep === 5" duration="0"/>
        <testCase name="PreviewFormDetails Component Modal Close Functions Coverage covers onDeleteClose function" duration="46"/>
        <testCase name="PreviewFormDetails Component Modal Close Functions Coverage covers setShowAddJobModal close function" duration="0"/>
        <testCase name="PreviewFormDetails Component Risk Form Specific Coverage handles approval_required with matching options" duration="0"/>
        <testCase name="PreviewFormDetails Component Risk Form Specific Coverage handles approval_required fallback to count display" duration="0"/>
        <testCase name="PreviewFormDetails Component Risk Form Specific Coverage handles different assessor values" duration="0"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers edit at-risk parameters button click (line 674-676)" duration="0"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers save to draft with risk type (line 799)" duration="0"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers button text for risk type (line 803)" duration="0"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers approval required length display (line 396)" duration="0"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers lines 184-186 with specific form configuration" duration="0"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers modal close functions by simulating component state changes" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers lines 184-186 with null/empty values" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers modal close functions with EditTemplateModal interaction" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers approval required display with many approvals" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers different assessor values" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers empty string values for task fields" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers all remaining edge cases" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers risk form type with template type fallback" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers useEffect error handling simulation" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers onClose function with editStep === 5" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers button click handler for last updated by" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers additional edge cases for comprehensive coverage" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests achieves maximum possible coverage with current constraints" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers getNamesByIds function with complex category structures" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers getAtRisk function with is_other scenarios" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers table column functions with edge cases" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers date formatting with various date formats" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers approval options with non-matching IDs" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers empty parameter arrays in getAtRisk" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers complex category and hazard structures" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers parameter handling with is_other scenarios" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers table rendering with many jobs" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers date formatting with edge cases" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers approval matching with mixed scenarios" duration="0"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers comprehensive edge case scenarios" duration="0"/>
    </file>
</testExecutions>